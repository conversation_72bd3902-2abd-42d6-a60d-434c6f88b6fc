import { z } from 'zod';

export const preguntaFormSchema = z.object({
  pregunta: z.string().min(1, 'La pregunta es obligatoria').max(500, 'Máximo 500 caracteres'),
  documentos: z.array(
    z.object({
      titulo: z.string().min(1),
      contenido: z.string().min(1),
      categoria: z.string().optional(),
      numero_tema: z.number().int().positive().optional(),
    })
  ).min(1, 'Debes seleccionar al menos un documento'),
});

// Esquema simplificado para los generadores que solo necesitan la petición
export const generatorFormSchema = z.object({
  peticion: z.string().min(1, 'La petición es obligatoria').max(500, 'Máximo 500 caracteres'),
});

export const testFormSchema = generatorFormSchema;
export const flashcardFormSchema = generatorFormSchema;
export const mindMapFormSchema = generatorFormSchema;
