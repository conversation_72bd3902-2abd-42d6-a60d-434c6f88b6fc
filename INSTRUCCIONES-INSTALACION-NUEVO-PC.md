# OposicionesIA - Instrucciones de Instalación para Nuevo PC

## Requisitos Previos

1. **Node.js** (versión 18 o superior)
   - <PERSON><PERSON><PERSON> desde: https://nodejs.org/
   - Verificar instalación: `node --version` y `npm --version`

2. **Git** (opcional, pero recomendado)
   - <PERSON><PERSON><PERSON> desde: https://git-scm.com/

## Pasos de Instalación

### 1. Extraer el archivo ZIP
- Extraer `OposicionesIA-Portable.zip` en la ubicación deseada
- Ejemplo: `C:\Proyectos\OposicionesIA\`

### 2. Instalar dependencias
Abrir terminal/cmd en la carpeta extraída y ejecutar:
```bash
npm install
```

### 3. Configurar variables de entorno
Crear un archivo `.env.local` en la raíz del proyecto con:
```
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase

# Gemini AI Configuration
GEMINI_API_KEY=tu_clave_api_de_gemini
```

### 4. Ejecutar la aplicación
```bash
npm run dev
```

La aplicación estará disponible en: http://localhost:3000

## Configuración de Supabase

### Crear proyecto en Supabase
1. Ir a https://supabase.com/
2. Crear una cuenta o iniciar sesión
3. Crear un nuevo proyecto
4. Obtener la URL y la clave anónima del proyecto

### Configurar base de datos
Ejecutar las siguientes consultas SQL en el editor de Supabase:

```sql
-- Crear tabla de documentos
CREATE TABLE documentos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  contenido TEXT NOT NULL,
  categoria TEXT,
  numero_tema INTEGER,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de tests
CREATE TABLE tests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de preguntas de test
CREATE TABLE preguntas_test (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  test_id UUID REFERENCES tests(id) ON DELETE CASCADE,
  pregunta TEXT NOT NULL,
  opcion_a TEXT NOT NULL,
  opcion_b TEXT NOT NULL,
  opcion_c TEXT NOT NULL,
  opcion_d TEXT NOT NULL,
  respuesta_correcta TEXT NOT NULL CHECK (respuesta_correcta IN ('a', 'b', 'c', 'd')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de colecciones de flashcards
CREATE TABLE colecciones_flashcards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  titulo TEXT NOT NULL,
  descripcion TEXT,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de flashcards
CREATE TABLE flashcards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  coleccion_id UUID REFERENCES colecciones_flashcards(id) ON DELETE CASCADE,
  pregunta TEXT NOT NULL,
  respuesta TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de estadísticas de estudio
CREATE TABLE estadisticas_estudio (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  flashcard_id UUID REFERENCES flashcards(id),
  dificultad TEXT CHECK (dificultad IN ('facil', 'normal', 'dificil')),
  fecha_estudio TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habilitar RLS (Row Level Security)
ALTER TABLE documentos ENABLE ROW LEVEL SECURITY;
ALTER TABLE tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE preguntas_test ENABLE ROW LEVEL SECURITY;
ALTER TABLE colecciones_flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;
ALTER TABLE estadisticas_estudio ENABLE ROW LEVEL SECURITY;

-- Políticas de seguridad
CREATE POLICY "Users can view own documents" ON documentos FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own documents" ON documentos FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own documents" ON documentos FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own documents" ON documentos FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own tests" ON tests FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own tests" ON tests FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own tests" ON tests FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own tests" ON tests FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view questions of own tests" ON preguntas_test FOR SELECT USING (
  EXISTS (SELECT 1 FROM tests WHERE tests.id = preguntas_test.test_id AND tests.user_id = auth.uid())
);
CREATE POLICY "Users can insert questions to own tests" ON preguntas_test FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM tests WHERE tests.id = preguntas_test.test_id AND tests.user_id = auth.uid())
);

CREATE POLICY "Users can view own flashcard collections" ON colecciones_flashcards FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own flashcard collections" ON colecciones_flashcards FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own flashcard collections" ON colecciones_flashcards FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own flashcard collections" ON colecciones_flashcards FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view flashcards of own collections" ON flashcards FOR SELECT USING (
  EXISTS (SELECT 1 FROM colecciones_flashcards WHERE colecciones_flashcards.id = flashcards.coleccion_id AND colecciones_flashcards.user_id = auth.uid())
);
CREATE POLICY "Users can insert flashcards to own collections" ON flashcards FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM colecciones_flashcards WHERE colecciones_flashcards.id = flashcards.coleccion_id AND colecciones_flashcards.user_id = auth.uid())
);

CREATE POLICY "Users can view own study statistics" ON estadisticas_estudio FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own study statistics" ON estadisticas_estudio FOR INSERT WITH CHECK (auth.uid() = user_id);
```

## Configuración de Gemini AI

1. Ir a https://makersuite.google.com/app/apikey
2. Crear una clave API de Gemini
3. Agregar la clave al archivo `.env.local`

## Solución de Problemas

### Error de dependencias
Si hay errores al instalar dependencias:
```bash
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### Error de puerto ocupado
Si el puerto 3000 está ocupado:
```bash
npm run dev -- -p 3001
```

### Problemas con Supabase
- Verificar que las URLs y claves sean correctas
- Asegurarse de que RLS esté configurado correctamente
- Verificar que el usuario tenga permisos

## Estructura del Proyecto

```
OposicionesIA/
├── src/
│   ├── app/                 # Páginas de Next.js
│   ├── components/          # Componentes React
│   ├── lib/                 # Librerías y utilidades
│   ├── config/              # Configuraciones
│   └── contexts/            # Contextos de React
├── public/                  # Archivos estáticos
├── package.json             # Dependencias del proyecto
├── tailwind.config.js       # Configuración de Tailwind CSS
├── tsconfig.json           # Configuración de TypeScript
└── .env.local              # Variables de entorno (crear)
```

## Comandos Útiles

- `npm run dev` - Ejecutar en modo desarrollo
- `npm run build` - Construir para producción
- `npm run start` - Ejecutar en modo producción
- `npm run lint` - Verificar código

## Soporte

Si encuentras problemas durante la instalación, verifica:
1. Versión de Node.js (debe ser 18+)
2. Configuración de variables de entorno
3. Conexión a internet para Supabase y Gemini
4. Permisos de escritura en la carpeta del proyecto
