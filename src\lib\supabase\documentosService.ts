import { supabase, Documento } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Obtiene todos los documentos del usuario actual ordenados por número de tema
 */
export async function obtenerDocumentos(): Promise<Documento[]> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('documentos')
      .select('*')
      .eq('user_id', user.id)
      .order('numero_tema', { ascending: true });

    if (error) {
      console.error('Error al obtener documentos:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener documentos:', error);
    return [];
  }
}

/**
 * Obtiene un documento específico por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerDocumentoPorId(id: string): Promise<Documento | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('documentos')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener documento:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener documento:', error);
    return null;
  }
}

/**
 * Guarda un nuevo documento en la base de datos asociado al usuario actual
 */
export async function guardarDocumento(documento: Omit<Documento, 'id' | 'creado_en' | 'actualizado_en' | 'user_id'>): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    // Añadir el user_id al documento
    const documentoConUsuario = {
      ...documento,
      user_id: user.id
    };

    const { data, error } = await supabase
      .from('documentos')
      .insert([documentoConUsuario])
      .select();

    if (error) {
      console.error('Error al guardar documento:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al guardar documento:', error);
    return null;
  }
}
