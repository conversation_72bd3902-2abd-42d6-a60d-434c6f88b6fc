'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/supabaseClient';
import { Session, User } from '@supabase/supabase-js';

interface MobileAuthState {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  isMobile: boolean;
  hasStorageSupport: boolean;
  sessionSource: 'supabase' | 'localStorage' | 'none';
}

export function useMobileAuth(): MobileAuthState {
  const [state, setState] = useState<MobileAuthState>({
    session: null,
    user: null,
    isLoading: true,
    isMobile: false,
    hasStorageSupport: false,
    sessionSource: 'none',
  });

  useEffect(() => {
    // Detectar si es dispositivo móvil
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );

    // Verificar soporte de localStorage
    const hasStorageSupport = typeof Storage !== 'undefined';

    setState(prev => ({
      ...prev,
      isMobile,
      hasStorageSupport,
    }));

    const initializeAuth = async () => {
      try {
        // Intentar obtener sesión de Supabase primero
        const { data: { session }, error } = await supabase.auth.getSession();

        if (session && !error) {
          setState(prev => ({
            ...prev,
            session,
            user: session.user,
            isLoading: false,
            sessionSource: 'supabase',
          }));
          return;
        }

        // Si no hay sesión en Supabase y estamos en móvil, verificar localStorage
        if (isMobile && hasStorageSupport) {
          try {
            const storedToken = localStorage.getItem('supabase.auth.token');
            if (storedToken) {
              // Esperar un poco y reintentar
              await new Promise(resolve => setTimeout(resolve, 500));

              const { data: { session: retrySession }, error: retryError } = await supabase.auth.getSession();

              if (retrySession && !retryError) {
                setState(prev => ({
                  ...prev,
                  session: retrySession,
                  user: retrySession.user,
                  isLoading: false,
                  sessionSource: 'localStorage',
                }));
                return;
              }
            }
          } catch (storageError) {
            // Error silencioso
          }
        }

        // No se encontró sesión válida
        setState(prev => ({
          ...prev,
          session: null,
          user: null,
          isLoading: false,
          sessionSource: 'none',
        }));

      } catch (error) {
        setState(prev => ({
          ...prev,
          session: null,
          user: null,
          isLoading: false,
          sessionSource: 'none',
        }));
      }
    };

    // Configurar listener de cambios de autenticación
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setState(prev => ({
          ...prev,
          session,
          user: session?.user || null,
          isLoading: false,
          sessionSource: session ? 'supabase' : 'none',
        }));
      }
    );

    // Inicializar autenticación
    initializeAuth();

    // Cleanup
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return state;
}
