// src/middleware.ts

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req: NextRequest) {
  // IMPORTANTE: Crear una respuesta base que Supabase puede modificar para añadir cookies
  const res = NextResponse.next();

  // IMPORTANTE: Inicializar el cliente Supabase para el middleware con req y res
  const supabase = createMiddlewareClient({ req, res });

  // IMPORTANTE: Obtener la sesión. Esto también refrescará el token si es necesario
  // y actualizará las cookies en `res` si hay cambios.
  const { data: { session } } = await supabase.auth.getSession();

  const requestedPath = req.nextUrl.pathname;

  // Verificar si hay una cookie de token de Supabase
  const cookieHeader = req.headers.get('cookie') || '';
  const hasSupabaseCookie = cookieHeader.includes('supabase.auth.token');

  // Para dispositivos móviles, ser más permisivo con la detección de sesión
  // Verificar también headers adicionales que pueden indicar una sesión activa
  const authHeader = req.headers.get('authorization');
  const hasAuthHeader = !!authHeader && authHeader.startsWith('Bearer ');

  // Determinar si hay una sesión basada en múltiples fuentes
  const hasSession = !!session || hasSupabaseCookie || hasAuthHeader;

  // Definir rutas públicas que no requieren autenticación
  const publicPaths = ['/login'];

  // Excluir rutas de API o recursos estáticos
  if (
    requestedPath.startsWith('/api/') ||
    requestedPath.startsWith('/_next/')
  ) {
    return res;
  }

  // --- LÓGICA DE AUTENTICACIÓN Y REDIRECCIÓN ---

  // CASO 1: Usuario autenticado intentando acceder a /login
  if (hasSession && requestedPath === '/login') {
    return NextResponse.redirect(new URL('/', req.url));
  }

  // CASO 2: Usuario no autenticado intentando acceder a una ruta protegida
  if (!hasSession && !publicPaths.includes(requestedPath)) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // CASO 3: Permitir el acceso en todos los demás casos
  // IMPORTANTE: Devolver la respuesta que puede tener cookies actualizadas
  return res;
}

// Configuración del matcher para definir en qué rutas se ejecutará el middleware.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     *
     * También puedes excluir rutas API específicas aquí si prefieres no hacerlo en el código del middleware,
     * pero manejarlo en el código da más flexibilidad si algunas rutas API necesitan auth y otras no.
     * Si todas las rutas /api/* están protegidas o no, puedes gestionarlo arriba.
     *
     * La expresión regular abajo intenta cubrir los casos más comunes:
     */
    '/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)',
    // Explicación de la regex mejorada:
    // /((?!             // Inicio de grupo de no coincidencia (negative lookahead)
    // _next/static      // No coincidir con _next/static
    // |_next/image      // O no coincidir con _next/image
    // |favicon.ico     // O no coincidir con favicon.ico
    // |manifest.json   // O no coincidir con manifest.json (común para PWAs)
    // |robots.txt      // O no coincidir con robots.txt
    // |.*\\..*         // O no coincidir con cualquier cosa que contenga un punto (archivos como .png, .css, etc.)
    // ).*)             // Fin del lookahead, coincide con cualquier otra cosa
  ],
};