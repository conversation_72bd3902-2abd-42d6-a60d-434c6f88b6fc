import React from 'react';
import { ColeccionFlashcards } from '@/lib/supabase';

interface FlashcardCollectionListProps {
  colecciones: ColeccionFlashcards[];
  coleccionSeleccionada: ColeccionFlashcards | null;
  onSeleccionarColeccion: (coleccion: ColeccionFlashcards) => void;
  isLoading: boolean;
}

const FlashcardCollectionList: React.FC<FlashcardCollectionListProps> = ({
  colecciones,
  coleccionSeleccionada,
  onSeleccionarColeccion,
  isLoading
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (colecciones.length === 0) {
    return (
      <div className="text-center p-4">
        <p className="text-gray-500">No hay colecciones de flashcards disponibles.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      {colecciones.map((coleccion) => (
        <div
          key={coleccion.id}
          className={`border rounded-lg p-4 cursor-pointer transition-all ${
            coleccionSeleccionada?.id === coleccion.id
              ? 'border-orange-500 bg-orange-50'
              : 'border-gray-200 hover:border-orange-300 hover:bg-orange-50/50'
          }`}
          onClick={() => onSeleccionarColeccion(coleccion)}
        >
          <h3 className="font-semibold text-lg">{coleccion.titulo}</h3>
          <p className="text-sm text-gray-500">
            Creada el {new Date(coleccion.creado_en).toLocaleDateString()}
          </p>
          {coleccion.descripcion && (
            <p className="text-sm mt-2 text-gray-700">{coleccion.descripcion}</p>
          )}
        </div>
      ))}
    </div>
  );
};

export default FlashcardCollectionList;
