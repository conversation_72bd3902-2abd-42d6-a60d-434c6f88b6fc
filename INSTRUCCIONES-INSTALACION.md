# 📚 Instrucciones para ejecutar la aplicación OposiAI

Este archivo contiene instrucciones detalladas para instalar y ejecutar la aplicación OposiAI en un nuevo equipo.

## ✅ Requisitos previos

- **Node.js** (versión 18 o superior) - [Descargar desde nodejs.org](https://nodejs.org/)
- **npm** (normalmente se instala con Node.js)
- **Navegador web moderno** (Chrome, Firefox, Safari, Edge)

## 🚀 Pasos para la instalación

### 1. **Descomprimir el archivo ZIP**

Descomprime el archivo `oposiciones-app.zip` en una carpeta de tu elección.

### 2. **Abrir terminal en la carpeta del proyecto**

- **Windows**: Abre PowerShell o Command Prompt y navega a la carpeta
- **macOS/Linux**: Abre Terminal y navega a la carpeta
- **Alternativa**: Haz clic derecho en la carpeta y selecciona "Abrir terminal aquí"

### 3. **Instalar dependencias**

Ejecuta el siguiente comando en la terminal:

```bash
npm install
```

⏱️ Este proceso puede tardar varios minutos dependiendo de tu conexión a internet.

### 4. **Configurar variables de entorno (YA INCLUIDAS)**

⚠️ **IMPORTANTE**: El archivo `.env.local` ya está incluido en el ZIP con las claves de API configuradas. 
**No necesitas modificar nada para que la aplicación funcione.**

Si necesitas usar tus propias claves de API, edita el archivo `.env.local` con:

```env
NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
NEXT_PUBLIC_GEMINI_API_KEY=tu_clave_api_de_gemini
```

### 5. **Ejecutar la aplicación en modo desarrollo**

```bash
npm run dev
```

🌐 La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

### 6. **Construir la aplicación para producción (opcional)**

```bash
npm run build
npm start
```

🌐 La aplicación estará disponible en [http://localhost:3000](http://localhost:3000)

## 📁 Estructura del proyecto

- `src/app`: Páginas y rutas de la aplicación
- `src/components`: Componentes React reutilizables
- `src/lib`: Bibliotecas y servicios (Supabase, Gemini, etc.)
- `src/config`: Archivos de configuración, incluyendo los prompts personalizados

## 🎯 Funcionalidades incluidas

- ✅ **Autenticación de usuarios** con Supabase
- ✅ **Generación de preguntas** con IA (Gemini)
- ✅ **Creación de flashcards** inteligentes
- ✅ **Generación de tests** personalizados
- ✅ **Mapas mentales** automáticos
- ✅ **Estadísticas de estudio** detalladas
- ✅ **Historial de conversaciones**
- ✅ **Subida de documentos** para estudio

## 🔧 Personalización de prompts

Los prompts personalizados para cada funcionalidad se encuentran en `src/config/prompts.ts`. 
Puedes modificarlos según tus necesidades específicas.

## ❗ Problemas comunes

### Error de conexión a Supabase
- Verifica que las variables de entorno sean correctas
- Asegúrate de tener conexión a internet

### Error al generar contenido con Gemini
- Asegúrate de que tu clave API de Gemini sea válida
- Verifica que tengas cuota disponible en tu cuenta de Google AI

### Problemas con las dependencias
Si encuentras errores al instalar dependencias, prueba con:
```bash
npm install --legacy-peer-deps
```

### Puerto 3000 ocupado
Si el puerto 3000 está ocupado, puedes usar otro puerto:
```bash
npm run dev -- -p 3001
```

## 🔒 Seguridad

- Las claves de API están incluidas para facilitar la instalación
- En un entorno de producción, considera usar tus propias claves
- Nunca compartas las claves de API públicamente

## 📞 Contacto

Si tienes problemas para instalar o ejecutar la aplicación, puedes contactar al desarrollador.

---

**¡Disfruta estudiando con OposiAI! 🎓**
