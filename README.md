# OposI v5 - Aplicación de Preparación de Oposiciones con IA

Una aplicación web moderna para la preparación de oposiciones que utiliza inteligencia artificial para ayudar a los estudiantes en su proceso de estudio.

## 🚀 Características

- **Autenticación segura** con Supabase
- **Preguntas y respuestas** generadas por IA
- **Mapas mentales** interactivos
- **Flashcards** para memorización
- **Interfaz moderna** con Next.js y Tailwind CSS
- **Responsive design** para todos los dispositivos

## 🛠️ Tecnologías Utilizadas

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Backend**: Supabase (Base de datos, Autenticación)
- **IA**: Google Gemini API
- **Deployment**: Vercel (recomendado)

## 📋 Requisitos Previos

- Node.js 18+
- npm o yarn
- Cuenta de Supabase
- API Key de Google Gemini

## 🔧 Instalación

1. Clona el repositorio:
```bash
git clone https://github.com/jugomrus/ReposI-v5.git
cd ReposI-v5
```

2. Instala las dependencias:
```bash
npm install
```

3. Configura las variables de entorno:
```bash
cp .env.example .env.local
```

Edita `.env.local` con tus credenciales:
```
NEXT_PUBLIC_SUPABASE_URL=tu_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_supabase_anon_key
GEMINI_API_KEY=tu_gemini_api_key
```

4. Ejecuta el servidor de desarrollo:
```bash
npm run dev
```

5. Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

## 🏗️ Estructura del Proyecto

```
src/
├── app/                 # App Router de Next.js
├── components/          # Componentes reutilizables
├── contexts/           # Contextos de React
├── lib/                # Utilidades y configuraciones
│   ├── gemini/         # Servicios de IA
│   └── supabase/       # Servicios de base de datos
└── middleware.ts       # Middleware de autenticación
```

## 🔐 Autenticación

La aplicación utiliza Supabase para la autenticación con las siguientes características:

- Inicio de sesión con email y contraseña
- Middleware de protección de rutas
- Gestión de sesiones con cookies HTTP
- Redirecciones automáticas basadas en el estado de autenticación

## 🤖 Funcionalidades de IA

- **Generación de preguntas** personalizadas
- **Creación de flashcards** automática
- **Mapas mentales** interactivos
- **Tests de evaluación** adaptativos

## 🚀 Deployment

### Vercel (Recomendado)

1. Conecta tu repositorio a Vercel
2. Configura las variables de entorno en Vercel
3. Deploy automático en cada push

### Otras plataformas

La aplicación es compatible con cualquier plataforma que soporte Next.js.

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📝 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 👨‍💻 Autor

**Jorge** - [@jugomrus](https://github.com/jugomrus)

## 🙏 Agradecimientos

- Supabase por la infraestructura backend
- Google Gemini por la IA
- Next.js por el framework
- Tailwind CSS por el sistema de diseño
