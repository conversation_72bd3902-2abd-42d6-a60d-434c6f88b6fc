import React, { useEffect, useState } from 'react';
import { Conversacion, obtenerConversaciones } from '../lib/supabase';

interface ConversationHistoryProps {
  onSelectConversation: (conversacionId: string) => void;
  conversacionActualId: string | null;
}

export default function ConversationHistory({
  onSelectConversation,
  conversacionActualId
}: ConversationHistoryProps) {
  const [conversaciones, setConversaciones] = useState<Conversacion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Función para cargar las conversaciones
  const cargarConversaciones = async () => {
    setIsLoading(true);
    try {
      const data = await obtenerConversaciones();
      setConversaciones(data);
    } catch (error) {
      console.error('Error al cargar conversaciones:', error);
      setError('No se pudieron cargar las conversaciones');
    } finally {
      setIsLoading(false);
    }
  };

  // Cargar conversaciones al montar el componente
  useEffect(() => {
    cargarConversaciones();
  }, []);

  // Recargar conversaciones cuando cambia la conversación actual
  useEffect(() => {
    if (conversacionActualId) {
      cargarConversaciones();
    }
  }, [conversacionActualId]);

  // Formatear la fecha para mostrarla en la lista
  const formatearFecha = (fechaStr: string): string => {
    const fecha = new Date(fechaStr);
    return fecha.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Generar un título automático si no existe
  const obtenerTitulo = (conversacion: Conversacion): string => {
    if (conversacion.titulo) {
      return conversacion.titulo;
    }
    return `Conversación del ${formatearFecha(conversacion.creado_en)}`;
  };

  return (
    <div className="mb-4">
      <h3 className="text-lg font-semibold mb-2">Historial de Conversaciones</h3>

      {isLoading ? (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Cargando conversaciones...</p>
        </div>
      ) : error ? (
        <div className="text-red-500 text-sm py-2">{error}</div>
      ) : conversaciones.length === 0 ? (
        <div className="text-gray-500 text-sm py-2">No hay conversaciones guardadas</div>
      ) : (
        <div className="max-h-60 overflow-y-auto border rounded-lg">
          <ul className="divide-y divide-gray-200">
            {conversaciones.map((conversacion) => (
              <li
                key={conversacion.id}
                className={`px-4 py-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                  conversacionActualId === conversacion.id ? 'bg-blue-50' : ''
                }`}
                onClick={() => onSelectConversation(conversacion.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="font-medium text-gray-800 truncate max-w-[70%]">
                    {obtenerTitulo(conversacion)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatearFecha(conversacion.actualizado_en)}
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {conversacionActualId === conversacion.id ? (
                    <span className="text-blue-600">Conversación actual</span>
                  ) : (
                    <span>Haz clic para cargar</span>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
