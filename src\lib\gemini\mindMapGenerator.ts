import { model, prepararDocumentos } from './geminiClient';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Construir un prompt avanzado para mapa mental interactivo
    const prompt = `
Eres "Mentor Opositor AI". Crea un mapa mental INTERACTIVO con cajas de texto expandibles basado en el contenido proporcionado.

CONTENIDO:
${contenidoDocumentos}

INSTRUCCIONES DEL USUARIO:
${instrucciones || 'Crea un mapa mental que organice los conceptos principales del contenido.'}

GENERA UN ARCHIVO HTML COMPLETO con mapa mental interactivo que tenga:

1. **TEXTO EN CAJAS/RECTÁNGULOS** (no círculos)
2. **FUNCIONALIDAD EXPANDIR/CONTRAER** al hacer clic
3. **SEPARACIÓN ADECUADA** entre niveles (mínimo 250px horizontal, 60px vertical)
4. **ANIMACIONES SUAVES** para reorganización
5. **ZOOM Y PAN** - arrastrar con ratón para mover la vista
6. **VALIDACIÓN ANTI-NaN** en todas las coordenadas

ESTRUCTURA REQUERIDA:

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mapa Mental Interactivo</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f9f9f9; overflow: hidden; }
        .link { fill: none; stroke: #999; stroke-width: 1.5px; }
        .node rect { stroke: #333; stroke-width: 1px; rx: 3; ry: 3; cursor: pointer; }
        .node text { font: 10px sans-serif; pointer-events: none; text-anchor: middle; dominant-baseline: central; }
        .node.collapsed rect { fill: #aec7e8; }
        .node.expanded rect { fill: #fff; }
    </style>
</head>
<body>
    <script>
        // DATOS DEL MAPA MENTAL - ADAPTA SEGÚN EL CONTENIDO
        const data = {
            name: "Tema Principal",
            children: [
                {
                    name: "Subtema 1",
                    children: [
                        { name: "Concepto 1.1" },
                        { name: "Concepto 1.2" }
                    ]
                },
                {
                    name: "Subtema 2",
                    children: [
                        { name: "Concepto 2.1" },
                        { name: "Concepto 2.2" }
                    ]
                }
            ]
        };

        // CONFIGURACIÓN
        const width = window.innerWidth || 1200;
        const height = window.innerHeight || 800;
        const duration = 750;
        const nodeHorizontalSeparation = 250;
        const nodeVerticalSeparation = 60;

        // CREAR SVG CON ZOOM Y PAN
        const svg = d3.select("body").append("svg")
            .attr("width", width)
            .attr("height", height)
            .call(d3.zoom()
                .scaleExtent([0.1, 3])
                .on("zoom", function(event) {
                    g.attr("transform", event.transform);
                }));

        const g = svg.append("g")
            .attr("transform", "translate(40,0)");

        // LAYOUT DE ÁRBOL CON MEJOR ESPACIADO
        const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);

        // PROCESAR DATOS
        const root = d3.hierarchy(data);
        root.x0 = height / 2;
        root.y0 = 0;

        // COLAPSAR NODOS INICIALMENTE
        if (root.children) {
            root.children.forEach(collapse);
        }

        function collapse(d) {
            if (d.children) {
                d._children = d.children;
                d._children.forEach(collapse);
                d.children = null;
            }
        }

        // FUNCIÓN PARA MANEJAR CLICS
        function handleClick(event, d) {
            if (d.children) {
                d._children = d.children;
                d.children = null;
            } else {
                d.children = d._children;
                d._children = null;
            }
            update(d);
        }

        // FUNCIÓN DIAGONAL PARA ENLACES
        function diagonal(s, d) {
            const sx = isNaN(s.y) ? 0 : s.y;
            const sy = isNaN(s.x) ? 0 : s.x;
            const dx = isNaN(d.y) ? 0 : d.y;
            const dy = isNaN(d.x) ? 0 : d.x;

            return \`M \${sx} \${sy}
                    C \${(sx + dx) / 2} \${sy},
                      \${(sx + dx) / 2} \${dy},
                      \${dx} \${dy}\`;
        }

        // FUNCIÓN UPDATE PRINCIPAL
        function update(source) {
            const treeData = treeLayout(root);
            const nodes = treeData.descendants();
            const links = treeData.links();

            // VALIDAR COORDENADAS
            nodes.forEach(d => {
                d.y = d.depth * nodeHorizontalSeparation;
                d.x = isNaN(d.x) ? 0 : d.x;
                d.y = isNaN(d.y) ? 0 : d.y;
                d.x0 = d.x0 || d.x;
                d.y0 = d.y0 || d.y;
            });

            // NODOS
            const node = g.selectAll("g.node")
                .data(nodes, d => d.id || (d.id = ++i));

            const nodeEnter = node.enter().append("g")
                .attr("class", "node")
                .attr("transform", d => \`translate(\${source.y0 || 0},\${source.x0 || 0})\`)
                .on("click", handleClick);

            // CALCULAR DIMENSIONES DE TEXTO CON MEJOR ESPACIADO
            nodeEnter.each(function(d) {
                const textLength = d.data.name.length;
                d.rectWidth = Math.max(textLength * 9 + 30, 80);
                d.rectHeight = 30;
            });

            // AÑADIR RECTÁNGULOS
            nodeEnter.append("rect")
                .attr("width", d => d.rectWidth)
                .attr("height", d => d.rectHeight)
                .attr("x", d => -d.rectWidth / 2)
                .attr("y", d => -d.rectHeight / 2)
                .style("fill", d => d._children ? "#aec7e8" : "#fff");

            // AÑADIR TEXTO
            nodeEnter.append("text")
                .text(d => d.data.name);

            // ACTUALIZAR NODOS
            const nodeUpdate = nodeEnter.merge(node);

            nodeUpdate.transition()
                .duration(duration)
                .attr("transform", d => \`translate(\${isNaN(d.y) ? 0 : d.y},\${isNaN(d.x) ? 0 : d.x})\`);

            nodeUpdate.select("rect")
                .style("fill", d => d._children ? "#aec7e8" : "#fff");

            // REMOVER NODOS
            const nodeExit = node.exit().transition()
                .duration(duration)
                .attr("transform", d => \`translate(\${source.y || 0},\${source.x || 0})\`)
                .remove();

            // ENLACES
            const link = g.selectAll("path.link")
                .data(links, d => d.target.id);

            const linkEnter = link.enter().insert("path", "g")
                .attr("class", "link")
                .attr("d", d => diagonal(source, source));

            const linkUpdate = linkEnter.merge(link);

            linkUpdate.transition()
                .duration(duration)
                .attr("d", d => diagonal(d.source, d.target));

            link.exit().transition()
                .duration(duration)
                .attr("d", d => diagonal(source, source))
                .remove();

            // GUARDAR POSICIONES
            nodes.forEach(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }

        let i = 0;
        update(root);
    </script>
</body>
</html>

IMPORTANTE:
- Adapta SOLO la estructura de datos (variable 'data') según el contenido
- Mantén la funcionalidad interactiva completa (clic para expandir/contraer)
- INCLUYE SIEMPRE zoom y pan con d3.zoom()
- USA separación mínima: 250px horizontal, 60px vertical
- Responde ÚNICAMENTE con el HTML completo
- NO incluyas explicaciones fuera del código
`;

    // Generar el mapa mental
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Extraer el HTML completo de la respuesta (el prompt genera HTML completo)
    // El mapa mental se genera como HTML con D3.js, no como JSON

    // Verificar que la respuesta contiene HTML válido
    if (!response.includes('<!DOCTYPE html>') && !response.includes('<html')) {
      console.error('Respuesta de Gemini para mapa mental:', response);
      throw new Error("La respuesta no contiene HTML válido para el mapa mental.");
    }

    // Retornar el HTML completo como string
    return response;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}
