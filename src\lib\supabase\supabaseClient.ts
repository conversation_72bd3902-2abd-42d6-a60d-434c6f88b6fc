import { createClient } from '@supabase/supabase-js';

// Configuración del cliente de Supabase
const supabaseUrl = 'https://fxnhpxjijinfuxxxplzj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4';

// Opciones adicionales para el cliente de Supabase
const supabaseOptions = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Configuración mejorada para dispositivos móviles
    storageKey: 'supabase.auth.token',
    storage: {
      getItem: (key: string) => {
        if (typeof window !== 'undefined') {
          // Intentar obtener de localStorage primero (más confiable en móviles)
          try {
            const localValue = localStorage.getItem(key);
            if (localValue) return localValue;
          } catch (e) {
            // Error silencioso
          }

          // Fallback a cookies
          return document.cookie
            .split('; ')
            .find((row) => row.startsWith(`${key}=`))
            ?.split('=')[1];
        }
        return null;
      },
      setItem: (key: string, value: string) => {
        if (typeof window !== 'undefined') {
          // Guardar en localStorage (más confiable en móviles)
          try {
            localStorage.setItem(key, value);
          } catch (e) {
            // Error silencioso
          }

          // También guardar en cookies como respaldo
          const isSecure = window.location.protocol === 'https:';
          document.cookie = `${key}=${value}; path=/; max-age=2592000; SameSite=Lax${isSecure ? '; secure' : ''}`;
        }
      },
      removeItem: (key: string) => {
        if (typeof window !== 'undefined') {
          // Remover de localStorage
          try {
            localStorage.removeItem(key);
          } catch (e) {
            // Error silencioso
          }

          // Remover de cookies
          document.cookie = `${key}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        }
      },
    },
  }
};

// Implementar patrón singleton para evitar múltiples instancias
let supabaseInstance: ReturnType<typeof createClient> | null = null;

export const getSupabaseClient = () => {
  if (supabaseInstance === null) {
    supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, supabaseOptions);
  }
  return supabaseInstance;
};

// Exportar una instancia única del cliente de Supabase
export const supabase = getSupabaseClient();

// Tipos comunes
export interface Documento {
  id: string;
  titulo: string;
  contenido: string;
  categoria?: string;
  numero_tema?: number;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
}

export interface Conversacion {
  id: string;
  titulo: string;
  creado_en: string;
  actualizado_en: string;
  activa?: boolean;
  user_id: string;
}

export interface Mensaje {
  id: string;
  conversacion_id: string;
  tipo: 'usuario' | 'ia';
  contenido: string;
  timestamp: string;
}

export interface Flashcard {
  id: string;
  coleccion_id: string;
  pregunta: string;
  respuesta: string;
  creado_en: string;
  actualizado_en: string;
}

export interface ColeccionFlashcards {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  actualizado_en: string;
  user_id: string;
}

export interface ProgresoFlashcard {
  id: string;
  flashcard_id: string;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  estado: string;
  ultima_revision: string;
  proxima_revision: string;
}

export interface FlashcardConProgreso extends Flashcard {
  debeEstudiar: boolean;
  progreso?: {
    factor_facilidad: number;
    intervalo: number;
    repeticiones: number;
    estado: string;
    proxima_revision: string;
  };
}

export interface RevisionHistorial {
  id: string;
  flashcard_id: string;
  dificultad: DificultadRespuesta;
  factor_facilidad: number;
  intervalo: number;
  repeticiones: number;
  fecha: string;
}

export interface Test {
  id: string;
  titulo: string;
  descripcion?: string;
  creado_en: string;
  documentos_ids?: string[];
  user_id: string;
}

export interface PreguntaTest {
  id: string;
  test_id: string;
  pregunta: string;
  opcion_a: string;
  opcion_b: string;
  opcion_c: string;
  opcion_d: string;
  respuesta_correcta: 'a' | 'b' | 'c' | 'd';
}

export interface EstadisticaTest {
  id: string;
  test_id: string;
  pregunta_id: string;
  respuesta_usuario: 'a' | 'b' | 'c' | 'd';
  es_correcta: boolean;
  fecha_respuesta: string;
}

export interface EstadisticasGeneralesTest {
  totalTests: number;
  totalPreguntas: number;
  totalRespuestasCorrectas: number;
  totalRespuestasIncorrectas: number;
  porcentajeAcierto: number;
}

export interface EstadisticasTestEspecifico {
  totalPreguntas: number;
  totalCorrectas: number;
  totalIncorrectas: number;
  porcentajeAcierto: number;
  fechasRealizacion: string[];
  preguntasMasFalladas: {
    preguntaId: string;
    pregunta: string;
    totalFallos: number;
    totalAciertos: number;
  }[];
}

export interface EstadisticasEstudio {
  totalSesiones: number;
  totalRevisiones: number;
  distribucionDificultad: {
    dificil: number;
    normal: number;
    facil: number;
  };
  progresoTiempo: {
    fecha: string;
    nuevas: number;
    aprendiendo: number;
    repasando: number;
    aprendidas: number;
  }[];
  tarjetasMasDificiles: {
    id: string;
    pregunta: string;
    dificil: number;
    normal: number;
    facil: number;
    totalRevisiones: number;
  }[];
}

export type DificultadRespuesta = 'dificil' | 'normal' | 'facil';
export type EstadoFlashcard = 'nuevo' | 'aprendiendo' | 'repasando' | 'aprendido';
