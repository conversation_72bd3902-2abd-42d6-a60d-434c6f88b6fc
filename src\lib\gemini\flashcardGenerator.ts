import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_FLASHCARDS } from '../../config/prompts';

/**
 * Genera flashcards a partir de los documentos
 */
export async function generarFlashcards(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  try {
    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos) {
      throw new Error("No se han proporcionado documentos para generar flashcards.");
    }

    // Construir el prompt para la IA usando el prompt personalizado
    // Reemplazar las variables en el prompt
    let prompt = PROMPT_FLASHCARDS
      .replace('{documentos}', contenidoDocumentos)
      .replace('{cantidad}', cantidad.toString());

    // Añadir instrucciones adicionales si se proporcionan
    if (instrucciones) {
      prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
    } else {
      prompt = prompt.replace('{instrucciones}', '');
    }

    // Generar las flashcards
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    // Extraer el JSON de la respuesta
    const jsonMatch = response.match(/\[\s*\{[\s\S]*\}\s*\]/);

    if (!jsonMatch) {
      throw new Error("No se pudo extraer el formato JSON de la respuesta.");
    }

    const flashcardsJson = jsonMatch[0];
    const flashcards = JSON.parse(flashcardsJson);

    // Validar el formato
    if (!Array.isArray(flashcards) || flashcards.length === 0) {
      throw new Error("El formato de las flashcards generadas no es válido.");
    }

    return flashcards;
  } catch (error) {
    console.error('Error al generar flashcards:', error);
    throw error;
  }
}
